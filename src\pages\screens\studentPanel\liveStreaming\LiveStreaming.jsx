// import React, { useState, useEffect, useCallback } from 'react';
// import { motion, AnimatePresence } from 'framer-motion';
// import {
//   Video,
//   Loader2,
//   Users,
//   Clock,
//   Play,
//   CheckCircle,
//   XCircle,
//   AlertCircle,
//   Send,
//   RefreshCw,
//   User,
//   MessageSquare
// } from 'lucide-react';
// import toast, { Toaster } from 'react-hot-toast';
// import io from 'socket.io-client';

// const LiveStreaming = () => {
//   // State management
//   const [activeStreams, setActiveStreams] = useState([]);
//   const [isLoadingStreams, setIsLoadingStreams] = useState(true);
//   const [permissionRequests, setPermissionRequests] = useState({});
//   const [permissionStatuses, setPermissionStatuses] = useState({});
//   const [isRequestingPermission, setIsRequestingPermission] = useState({});
//   const [showRequestModal, setShowRequestModal] = useState(false);
//   const [selectedStream, setSelectedStream] = useState(null);
//   const [requestMessage, setRequestMessage] = useState('');
//   const [studentInfo, setStudentInfo] = useState(null);
//   const [facultyId, setFacultyId] = useState(null);
//   const [socket, setSocket] = useState(null);

//   // API Base URL
//   const API_BASE_URL = 'https://sasthra.in';

//   // Get user info from session storage
//   const userId = sessionStorage.getItem('userId');
//   const userName = sessionStorage.getItem('name') || 'Student';
//   const token = sessionStorage.getItem('token');

//   // Helper function to get headers with authorization
//   const getAuthHeaders = () => ({
//     'Content-Type': 'application/json',
//     ...(token && { 'Authorization': `Bearer ${token}` })
//   });

//   // API Functions
//   const fetchActiveStreams = useCallback(async () => {
//     try {
//       setIsLoadingStreams(true);
//       console.log('🔍 Fetching active streams from:', `${API_BASE_URL}/active-streams`);

//       const response = await fetch(`${API_BASE_URL}/active-streams`);
//       console.log('📡 Response status:', response.status);

//       if (!response.ok) {
//         throw new Error(`HTTP error! status: ${response.status}`);
//       }

//       const data = await response.json();
//       console.log('📊 Active streams data:', data);

//       // Handle both possible response formats
//       let streams = [];
//       if (data.success && data.active_streams) {
//         streams = data.active_streams;
//       } else if (Array.isArray(data.active_streams)) {
//         streams = data.active_streams;
//       } else if (Array.isArray(data)) {
//         streams = data;
//       }

//       console.log('🎥 Processed streams:', streams);
//       setActiveStreams(streams);

//       // Check permission status for each stream
//       for (const stream of streams) {
//         if (stream.session_id) {
//           await checkPermissionStatus(stream.session_id);
//         }
//       }
//     } catch (error) {
//       console.error('❌ Error fetching active streams:', error);
//       toast.error(`Failed to load active streams: ${error.message}`);
//       setActiveStreams([]);
//     } finally {
//       setIsLoadingStreams(false);
//     }
//   }, []);

//   const checkPermissionStatus = useCallback(async (sessionId) => {
//     if (!userId) {
//       console.log('⚠️ No userId available for permission check');
//       return;
//     }

//     try {
//       console.log(`🔍 Checking permission status for session: ${sessionId}`);
//       const response = await fetch(`${API_BASE_URL}/api/stream-permission-status/${userId}/${sessionId}`);

//       if (!response.ok) {
//         console.log(`⚠️ Permission check failed with status: ${response.status}`);
//         return;
//       }

//       const data = await response.json();
//       console.log(`✅ Permission status for ${sessionId}:`, data);

//       setPermissionStatuses(prev => ({
//         ...prev,
//         [sessionId]: data
//       }));
//     } catch (error) {
//       console.error('❌ Error checking permission status:', error);
//     }
//   }, [userId]);

//   const fetchStudentDashboard = useCallback(async () => {
//     try {
//       console.log('🔍 Fetching student dashboard...');
//       const response = await fetch(`${API_BASE_URL}/student-dashboard`, {
//         headers: getAuthHeaders()
//       });

//       if (!response.ok) {
//         throw new Error(`HTTP error! status: ${response.status}`);
//       }

//       const data = await response.json();
//       console.log('📊 Student dashboard data:', data);

//       if (data.student) {
//         setStudentInfo(data.student);
//         console.log('👤 Student info set:', data.student);

//         // Extract faculty ID from the response
//         if (data.kota_teachers && data.kota_teachers.length > 0) {
//           const facultyIdFromResponse = data.kota_teachers[0].id;
//           setFacultyId(facultyIdFromResponse);
//           console.log('👨‍🏫 Faculty ID set:', facultyIdFromResponse);
//         } else {
//           console.log('⚠️ No kota_teachers found in response');
//         }
//       } else {
//         console.log('⚠️ No student data in response');
//       }
//     } catch (error) {
//       console.error('❌ Error fetching student dashboard:', error);
//       toast.error(`Failed to load student information: ${error.message}`);
//     }
//   }, []);

//   const requestStreamPermission = useCallback(async (sessionId, message) => {
//     if (!userId || !facultyId) {
//       toast.error('Missing user or faculty information');
//       return false;
//     }

//     try {
//       setIsRequestingPermission(prev => ({ ...prev, [sessionId]: true }));

//       const response = await fetch(`${API_BASE_URL}/api/stream-permission-request`, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           session_id: sessionId,
//           student_id: userId,
//           faculty_id: facultyId,
//           message: message || 'Please allow me to join the live stream'
//         })
//       });

//       const data = await response.json();

//       if (data.success) {
//         toast.success('Permission request sent successfully!');
//         setPermissionRequests(prev => ({
//           ...prev,
//           [sessionId]: data.request_id
//         }));
//         // Update permission status
//         await checkPermissionStatus(sessionId);
//         return true;
//       } else {
//         toast.error(data.message || 'Failed to send permission request');
//         return false;
//       }
//     } catch (error) {
//       console.error('Error requesting permission:', error);
//       toast.error('Failed to send permission request');
//       return false;
//     } finally {
//       setIsRequestingPermission(prev => ({ ...prev, [sessionId]: false }));
//     }
//   }, [userId, facultyId, checkPermissionStatus]);

//   const joinStream = useCallback(async (stream) => {
//     try {
//       const response = await fetch(`${API_BASE_URL}/api/livekit/join`, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           session_id: stream.session_id,
//           user_id: userId,
//           user_name: userName,
//           user_role: 'student'
//         })
//       });

//       const data = await response.json();

//       if (data.success || data.token) {
//         toast.success('Joining stream...');
//         // Here you would integrate with your LiveKit client
//         console.log('LiveKit token:', data.token);
//         console.log('LiveKit URL:', data.livekit_url);
//         // TODO: Implement actual stream joining logic
//       } else {
//         toast.error(data.message || 'Failed to join stream');
//       }
//     } catch (error) {
//       console.error('Error joining stream:', error);
//       toast.error('Failed to join stream');
//     }
//   }, [userId, userName]);

//   // useEffect hooks
//   useEffect(() => {
//     console.log('🚀 Component mounted, fetching student dashboard...');
//     fetchStudentDashboard();
//   }, [fetchStudentDashboard]);

//   // Fallback: Fetch streams even without faculty ID (for viewing only)
//   useEffect(() => {
//     console.log('🚀 Component mounted, fetching active streams as fallback...');
//     fetchActiveStreams();
//   }, [fetchActiveStreams]);

//   useEffect(() => {
//     console.log('🔄 useEffect triggered - facultyId:', facultyId);
//     if (facultyId) {
//       console.log('✅ Faculty ID available, fetching active streams...');
//       fetchActiveStreams();
//       // Set up polling for active streams
//       const interval = setInterval(() => {
//         console.log('🔄 Polling for active streams...');
//         fetchActiveStreams();
//       }, 30000); // Poll every 30 seconds
//       return () => {
//         console.log('🛑 Clearing active streams polling interval');
//         clearInterval(interval);
//       };
//     } else {
//       console.log('⚠️ No faculty ID available, not fetching streams');
//     }
//   }, [facultyId, fetchActiveStreams]);

//   // Socket.IO setup for real-time notifications
//   useEffect(() => {
//     if (userId) {
//       const newSocket = io(API_BASE_URL, {
//         transports: ['websocket', 'polling']
//       });

//       newSocket.on('connect', () => {
//         console.log('🔌 Student connected to Socket.IO');
//         // Join student room for notifications
//         newSocket.emit('join_student_room', { student_id: userId });
//       });

//       newSocket.on('stream_permission_approved', (data) => {
//         console.log('✅ Permission approved:', data);
//         toast.success('Permission approved! You can now join the stream.', {
//           duration: 5000,
//           icon: '🎉'
//         });

//         // Update permission status
//         setPermissionStatuses(prev => ({
//           ...prev,
//           [data.session_id]: {
//             status: 'approved',
//             can_join: true,
//             response_message: data.message,
//             approved_at: data.approved_at
//           }
//         }));
//       });

//       newSocket.on('stream_permission_rejected', (data) => {
//         console.log('❌ Permission rejected:', data);
//         toast.error('Permission request rejected.', {
//           duration: 5000,
//           icon: '😞'
//         });

//         // Update permission status
//         setPermissionStatuses(prev => ({
//           ...prev,
//           [data.session_id]: {
//             status: 'rejected',
//             can_join: false,
//             response_message: data.message,
//             rejected_at: data.rejected_at
//           }
//         }));
//       });

//       newSocket.on('disconnect', () => {
//         console.log('🔌 Student disconnected from Socket.IO');
//       });

//       setSocket(newSocket);

//       return () => {
//         newSocket.disconnect();
//         setSocket(null);
//       };
//     }
//   }, [userId]);

//   // Helper functions
//   const formatUptime = (seconds) => {
//     const hours = Math.floor(seconds / 3600);
//     const minutes = Math.floor((seconds % 3600) / 60);
//     if (hours > 0) {
//       return `${hours}h ${minutes}m`;
//     }
//     return `${minutes}m`;
//   };

//   const getPermissionStatusIcon = (status) => {
//     switch (status) {
//       case 'approved':
//         return <CheckCircle className="w-5 h-5 text-green-500" />;
//       case 'rejected':
//         return <XCircle className="w-5 h-5 text-red-500" />;
//       case 'pending':
//         return <AlertCircle className="w-5 h-5 text-yellow-500" />;
//       default:
//         return null;
//     }
//   };

//   const getPermissionStatusText = (status) => {
//     switch (status) {
//       case 'approved':
//         return 'Approved';
//       case 'rejected':
//         return 'Rejected';
//       case 'pending':
//         return 'Pending';
//       default:
//         return 'Request Permission';
//     }
//   };

//   const handleRequestPermission = (stream) => {
//     setSelectedStream(stream);
//     setRequestMessage('');
//     setShowRequestModal(true);
//   };

//   const handleSubmitRequest = async () => {
//     if (!selectedStream) return;

//     const success = await requestStreamPermission(
//       selectedStream.session_id,
//       requestMessage || `Please allow me to join ${selectedStream.teacher_name}'s live class`
//     );

//     if (success) {
//       setShowRequestModal(false);
//       setSelectedStream(null);
//       setRequestMessage('');
//     }
//   };

//   // Animation variants
//   const containerVariants = {
//     hidden: { opacity: 0 },
//     visible: {
//       opacity: 1,
//       transition: {
//         staggerChildren: 0.1
//       }
//     }
//   };

//   const cardVariants = {
//     hidden: { opacity: 0, y: 20 },
//     visible: {
//       opacity: 1,
//       y: 0,
//       transition: { duration: 0.5, ease: 'easeOut' }
//     }
//   };

//   const streamCardVariants = {
//     hidden: { opacity: 0, scale: 0.95 },
//     visible: {
//       opacity: 1,
//       scale: 1,
//       transition: { duration: 0.3 }
//     },
//     hover: {
//       y: -5,
//       scale: 1.02,
//       transition: { duration: 0.2 }
//     }
//   };

//   const buttonVariants = {
//     idle: { scale: 1 },
//     hover: { scale: 1.05 },
//     tap: { scale: 0.95 }
//   };

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 p-4 sm:p-8">
//       <Toaster position="top-right" />

//       <motion.div
//         variants={containerVariants}
//         initial="hidden"
//         animate="visible"
//         className="max-w-6xl mx-auto">

//         {/* Header */}
//         <motion.header
//           variants={cardVariants}
//           className="text-center mb-8">
//           <div className="flex items-center justify-center gap-3 mb-4">
//             <Video className="h-12 w-12 text-indigo-600" />
//             <h1 className="text-4xl font-extrabold text-gray-900">Live Streaming</h1>
//           </div>
//           <p className="text-gray-600 text-xl">
//             Join interactive live sessions from your teachers
//           </p>
//         </motion.header>

//         {/* Loading State */}
//         {isLoadingStreams && (
//           <motion.div
//             variants={cardVariants}
//             className="bg-white/90 backdrop-blur-xl shadow-xl rounded-2xl p-8 text-center">
//             <Loader2 className="h-12 w-12 text-indigo-500 animate-spin mx-auto mb-4" />
//             <p className="text-lg text-gray-700">Loading active streams...</p>
//           </motion.div>
//         )}

//         {/* No Streams Available */}
//         {!isLoadingStreams && activeStreams.length === 0 && (
//           <motion.div
//             variants={cardVariants}
//             className="bg-white/90 backdrop-blur-xl shadow-xl rounded-2xl p-8 text-center">
//             <Video className="h-16 w-16 text-gray-400 mx-auto mb-4" />
//             <h3 className="text-xl font-semibold text-gray-700 mb-2">No Active Streams</h3>
//             <p className="text-gray-600 mb-6">
//               There are currently no live streams available. Check back later or contact your faculty.
//             </p>
//             <motion.button
//               variants={buttonVariants}
//               whileHover="hover"
//               whileTap="tap"
//               onClick={fetchActiveStreams}
//               className="bg-indigo-600 text-white px-6 py-3 rounded-lg font-medium flex items-center gap-2 mx-auto">
//               <RefreshCw className="h-5 w-5" />
//               Refresh
//             </motion.button>
//           </motion.div>
//         )}

//         {/* Active Streams Grid */}
//         {!isLoadingStreams && activeStreams.length > 0 && (
//           <motion.div
//             variants={cardVariants}
//             className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
//             {activeStreams.map((stream) => {
//               const permissionStatus = permissionStatuses[stream.session_id];
//               const isRequesting = isRequestingPermission[stream.session_id];

//               return (
//                 <motion.div
//                   key={stream.session_id}
//                   variants={streamCardVariants}
//                   whileHover="hover"
//                   className="bg-white/90 backdrop-blur-xl shadow-xl rounded-2xl p-6 border border-gray-100">

//                   {/* Stream Header */}
//                   <div className="flex items-center gap-3 mb-4">
//                     <div className="p-2 bg-red-100 rounded-lg">
//                       <Video className="h-6 w-6 text-red-600" />
//                     </div>
//                     <div className="flex-1">
//                       <h3 className="font-semibold text-gray-900 truncate">
//                         {stream.teacher_name}'s Live Class
//                       </h3>
//                       <div className="flex items-center gap-2 text-sm text-gray-600">
//                         <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
//                         <span>Live</span>
//                       </div>
//                     </div>
//                   </div>

//                   {/* Stream Info */}
//                   <div className="space-y-3 mb-6">
//                     <div className="flex items-center gap-2 text-sm text-gray-600">
//                       <User className="h-4 w-4" />
//                       <span>Teacher: {stream.teacher_name}</span>
//                     </div>
//                     <div className="flex items-center gap-2 text-sm text-gray-600">
//                       <Users className="h-4 w-4" />
//                       <span>{stream.viewer_count} viewers</span>
//                     </div>
//                     <div className="flex items-center gap-2 text-sm text-gray-600">
//                       <Clock className="h-4 w-4" />
//                       <span>Live for {formatUptime(stream.uptime)}</span>
//                     </div>
//                     <div className="flex items-center gap-2 text-sm text-gray-600">
//                       <Play className="h-4 w-4" />
//                       <span>Quality: {stream.quality}</span>
//                     </div>
//                   </div>

//                   {/* Permission Status */}
//                   {permissionStatus && (
//                     <div className="mb-4 p-3 rounded-lg bg-gray-50">
//                       <div className="flex items-center gap-2">
//                         {getPermissionStatusIcon(permissionStatus.status)}
//                         <span className="text-sm font-medium">
//                           {getPermissionStatusText(permissionStatus.status)}
//                         </span>
//                       </div>
//                       {permissionStatus.response_message && (
//                         <p className="text-sm text-gray-600 mt-1">
//                           {permissionStatus.response_message}
//                         </p>
//                       )}
//                     </div>
//                   )}

//                   {/* Action Buttons */}
//                   <div className="space-y-2">
//                     {permissionStatus?.status === 'approved' ? (
//                       <motion.button
//                         variants={buttonVariants}
//                         whileHover="hover"
//                         whileTap="tap"
//                         onClick={() => joinStream(stream)}
//                         className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center gap-2">
//                         <Play className="h-5 w-5" />
//                         Join Stream
//                       </motion.button>
//                     ) : permissionStatus?.status === 'pending' ? (
//                       <div className="w-full bg-yellow-100 text-yellow-800 py-3 px-4 rounded-lg font-medium text-center">
//                         Permission Pending...
//                       </div>
//                     ) : permissionStatus?.status === 'rejected' ? (
//                       <div className="w-full bg-red-100 text-red-800 py-3 px-4 rounded-lg font-medium text-center">
//                         Permission Rejected
//                       </div>
//                     ) : (
//                       <motion.button
//                         variants={buttonVariants}
//                         whileHover="hover"
//                         whileTap="tap"
//                         onClick={() => handleRequestPermission(stream)}
//                         disabled={isRequesting}
//                         className="w-full bg-[var(--color-student)] text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center gap-2 disabled:opacity-50">
//                         {isRequesting ? (
//                           <>
//                             <Loader2 className="h-5 w-5 animate-spin" />
//                             Requesting...
//                           </>
//                         ) : (
//                           <>
//                             <Send className="h-5 w-5" />
//                             Request Permission
//                           </>
//                         )}
//                       </motion.button>
//                     )}
//                   </div>
//                 </motion.div>
//               );
//             })}
//           </motion.div>
//         )}

//         {/* Request Permission Modal */}
//         <AnimatePresence>
//           {showRequestModal && selectedStream && (
//             <motion.div
//               initial={{ opacity: 0 }}
//               animate={{ opacity: 1 }}
//               exit={{ opacity: 0 }}
//               className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
//               onClick={() => setShowRequestModal(false)}>

//               <motion.div
//                 initial={{ scale: 0.95, opacity: 0 }}
//                 animate={{ scale: 1, opacity: 1 }}
//                 exit={{ scale: 0.95, opacity: 0 }}
//                 onClick={(e) => e.stopPropagation()}
//                 className="bg-white rounded-2xl p-6 w-full max-w-md">

//                 <div className="flex items-center gap-3 mb-4">
//                   <MessageSquare className="h-6 w-6 text-indigo-600" />
//                   <h3 className="text-xl font-semibold">Request Permission</h3>
//                 </div>

//                 <p className="text-gray-600 mb-4">
//                   Send a request to join <strong>{selectedStream.teacher_name}'s</strong> live class
//                 </p>

//                 <textarea
//                   value={requestMessage}
//                   onChange={(e) => setRequestMessage(e.target.value)}
//                   placeholder="Please allow me to join the live class..."
//                   className="w-full p-3 border border-gray-300 rounded-lg resize-none h-24 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
//                 />

//                 <div className="flex gap-3 mt-6">
//                   <motion.button
//                     variants={buttonVariants}
//                     whileHover="hover"
//                     whileTap="tap"
//                     onClick={() => setShowRequestModal(false)}
//                     className="flex-1 bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium">
//                     Cancel
//                   </motion.button>
//                   <motion.button
//                     variants={buttonVariants}
//                     whileHover="hover"
//                     whileTap="tap"
//                     onClick={handleSubmitRequest}
//                     className="flex-1 bg-[var(--color-student)] text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center gap-2">
//                     <Send className="h-4 w-4" />
//                     Send Request
//                   </motion.button>
//                 </div>
//               </motion.div>
//             </motion.div>
//           )}
//         </AnimatePresence>
//       </motion.div>
//     </div>
//   );
// };

// export default LiveStreaming;




import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Video,
  Loader2,
  Users,
  Clock,
  Play,
  CheckCircle,
  XCircle,
  AlertCircle,
  Send,
  RefreshCw,
  User,
  MessageSquare
} from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';
import io from 'socket.io-client';

const LiveStreaming = () => {
  // State management
  const [activeStreams, setActiveStreams] = useState([]);
  const [isLoadingStreams, setIsLoadingStreams] = useState(true);
  const [permissionRequests, setPermissionRequests] = useState({});
  const [permissionStatuses, setPermissionStatuses] = useState({});
  const [isRequestingPermission, setIsRequestingPermission] = useState({});
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [selectedStream, setSelectedStream] = useState(null);
  const [requestMessage, setRequestMessage] = useState('');
  const [studentInfo, setStudentInfo] = useState(null);
  const [facultyId, setFacultyId] = useState(null);
  const [allFacultyIds, setAllFacultyIds] = useState([]);
  const [socket, setSocket] = useState(null);

  // API Base URL
  const API_BASE_URL = 'https://sasthra.in';

  // Get user info from session storage
  const userId = sessionStorage.getItem('userId');
  const userName = sessionStorage.getItem('name') || 'Student';
  const token = sessionStorage.getItem('token');

  // Helper function to get headers with authorization
  const getAuthHeaders = () => ({
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  });

  // API Functions
  const fetchActiveStreams = useCallback(async () => {
    try {
      setIsLoadingStreams(true);
      console.log('🔍 Fetching active streams from:', `${API_BASE_URL}/active-streams`);

      const response = await fetch(`${API_BASE_URL}/active-streams`);
      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📊 Active streams data:', data);

      // Handle both possible response formats
      let streams = [];
      if (data.success && data.active_streams) {
        streams = data.active_streams;
      } else if (Array.isArray(data.active_streams)) {
        streams = data.active_streams;
      } else if (Array.isArray(data)) {
        streams = data;
      }

      console.log('🎥 Processed streams:', streams);
      setActiveStreams(streams);

      // Check permission status for each stream
      for (const stream of streams) {
        if (stream.session_id) {
          await checkPermissionStatus(stream.session_id);
        }
      }
    } catch (error) {
      console.error('❌ Error fetching active streams:', error);
      toast.error(`Failed to load active streams: ${error.message}`);
      setActiveStreams([]);
    } finally {
      setIsLoadingStreams(false);
    }
  }, []);

  const checkPermissionStatus = useCallback(async (sessionId) => {
    if (!userId) {
      console.log('⚠️ No userId available for permission check');
      return;
    }

    try {
      console.log(`🔍 Checking permission status for session: ${sessionId}`);
      const response = await fetch(`${API_BASE_URL}/api/stream-permission-status/${userId}/${sessionId}`);

      if (!response.ok) {
        console.log(`⚠️ Permission check failed with status: ${response.status}`);
        return;
      }

      const data = await response.json();
      console.log(`✅ Permission status for ${sessionId}:`, data);

      setPermissionStatuses(prev => ({
        ...prev,
        [sessionId]: data
      }));
    } catch (error) {
      console.error('❌ Error checking permission status:', error);
    }
  }, [userId]);

  const fetchStudentDashboard = useCallback(async () => {
    try {
      console.log('🔍 Fetching student dashboard...');
      const response = await fetch(`${API_BASE_URL}/student-dashboard`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📊 Student dashboard data:', data);

      if (data.student) {
        setStudentInfo(data.student);
        console.log('👤 Student info set:', data.student);

        // Extract faculty ID from the response - check multiple possible fields
        let facultyIdFromResponse = null;
        let allFacultyIds = [];

        // Check faculty field first (as you mentioned it should be data.faculty)
        if (data.faculty && Array.isArray(data.faculty) && data.faculty.length > 0) {
          facultyIdFromResponse = data.faculty[0].id;
          allFacultyIds = data.faculty.map(f => f.id);
          console.log('👨‍🏫 Faculty ID from faculty field:', facultyIdFromResponse);
          console.log('👥 All faculty IDs available:', allFacultyIds);
        }
        // Fallback to kota_teachers if faculty field is empty
        else if (data.kota_teachers && Array.isArray(data.kota_teachers) && data.kota_teachers.length > 0) {
          facultyIdFromResponse = data.kota_teachers[0].id;
          allFacultyIds = data.kota_teachers.map(f => f.id);
          console.log('👨‍🏫 Faculty ID from kota_teachers (fallback):', facultyIdFromResponse);
          console.log('👥 All kota_teacher IDs available:', allFacultyIds);
        } else {
          console.log('⚠️ No valid faculty found in response:', {
            faculty: data.faculty,
            kota_teachers: data.kota_teachers
          });
        }

        if (facultyIdFromResponse) {
          setFacultyId(facultyIdFromResponse);
          console.log('✅ Faculty ID successfully set:', facultyIdFromResponse);

          // Store all faculty IDs for alternative approach
          setAllFacultyIds(allFacultyIds);
          console.log('📋 All faculty IDs stored for broadcast approach');
        } else {
          console.error('❌ No faculty ID could be extracted from dashboard');
          toast.error('No faculty information available. Please contact support.');
        }
      } else {
        console.log('⚠️ No student data in response');
        toast.error('No student information found. Please try again later.');
      }
    } catch (error) {
      console.error('❌ Error fetching student dashboard:', error);
      toast.error(`Failed to load student information: ${error.message}`);
    }
  }, []);

  const requestStreamPermission = useCallback(async (sessionId, message) => {
    console.log('🔐 Attempting to request stream permission with:', { userId, sessionId });
    console.log('🔍 All available faculty IDs:', allFacultyIds);
    console.log('🔍 Student info:', studentInfo);

    if (!userId) {
      console.error('❌ Missing userId');
      toast.error('User information is missing. Please log in again.');
      return false;
    }

    // Check if we have any faculty IDs to send to
    const facultyIdsToSendTo = allFacultyIds && allFacultyIds.length > 0 ? allFacultyIds : (facultyId ? [facultyId] : []);

    if (facultyIdsToSendTo.length === 0) {
      console.error('❌ No faculty IDs available');
      console.log('🔍 Debugging faculty ID issue:');
      console.log('  - allFacultyIds:', allFacultyIds);
      console.log('  - facultyId:', facultyId);
      console.log('  - studentInfo:', studentInfo);
      toast.error('No faculty information available. Please try refreshing the page.');
      return false;
    }

    console.log(`📡 Sending permission request to ${facultyIdsToSendTo.length} faculty members:`, facultyIdsToSendTo);

    try {
      setIsRequestingPermission(prev => ({ ...prev, [sessionId]: true }));

      let successCount = 0;
      let lastRequestId = null;
      const errors = [];

      // Send permission request to ALL faculty members
      for (let i = 0; i < facultyIdsToSendTo.length; i++) {
        const currentFacultyId = facultyIdsToSendTo[i];

        const requestData = {
          session_id: sessionId,
          student_id: userId,
          faculty_id: currentFacultyId,
          message: message || 'Please allow me to join the live stream'
        };

        console.log(`📤 Sending permission request ${i + 1}/${facultyIdsToSendTo.length} to faculty:`, currentFacultyId);
        console.log('📋 Request data:', requestData);

        try {
          const response = await fetch(`${API_BASE_URL}/api/stream-permission-request`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
          });

          console.log(`📡 Response status for faculty ${currentFacultyId}:`, response.status);

          if (!response.ok) {
            const errorText = await response.text();
            console.error(`❌ Request failed for faculty ${currentFacultyId}:`, errorText);
            errors.push(`Faculty ${i + 1}: ${errorText}`);
            continue; // Continue to next faculty
          }

          const data = await response.json();
          console.log(`📊 Response data for faculty ${currentFacultyId}:`, data);

          if (data.success) {
            successCount++;
            lastRequestId = data.request_id;
            console.log(`✅ Permission request sent successfully to faculty ${currentFacultyId}`);
          } else {
            console.error(`❌ Request failed for faculty ${currentFacultyId}:`, data.message);
            errors.push(`Faculty ${i + 1}: ${data.message}`);
          }
        } catch (error) {
          console.error(`❌ Error sending to faculty ${currentFacultyId}:`, error);
          errors.push(`Faculty ${i + 1}: ${error.message}`);
        }

        // Small delay between requests to avoid overwhelming the server
        if (i < facultyIdsToSendTo.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // Handle results
      if (successCount > 0) {
        console.log(`✅ Permission requests sent successfully to ${successCount}/${facultyIdsToSendTo.length} faculty members`);
        toast.success(`Permission request sent to ${successCount} faculty member${successCount > 1 ? 's' : ''}!`);

        if (lastRequestId) {
          setPermissionRequests(prev => ({
            ...prev,
            [sessionId]: lastRequestId
          }));
        }

        // Update permission status
        await checkPermissionStatus(sessionId);
        return true;
      } else {
        console.error('❌ All permission requests failed');
        console.error('❌ Errors:', errors);
        toast.error(`Failed to send permission requests. Errors: ${errors.join(', ')}`);
        return false;
      }
    } catch (error) {
      console.error('❌ Error requesting permission:', error);
      toast.error(`Failed to send permission request: ${error.message}`);
      return false;
    } finally {
      setIsRequestingPermission(prev => ({ ...prev, [sessionId]: false }));
    }
  }, [userId, facultyId, allFacultyIds, checkPermissionStatus]);

  const joinStream = useCallback(async (stream) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/livekit/join`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: stream.session_id,
          user_id: userId,
          user_name: userName,
          user_role: 'student'
        })
      });

      const data = await response.json();

      if (data.success || data.token) {
        toast.success('Joining stream...');
        console.log('LiveKit token:', data.token);
        console.log('LiveKit URL:', data.livekit_url);
        // TODO: Implement actual stream joining logic
      } else {
        toast.error(data.message || 'Failed to join stream');
      }
    } catch (error) {
      console.error('❌ Error joining stream:', error);
      toast.error('Failed to join stream');
    }
  }, [userId, userName]);

  // useEffect hooks
  useEffect(() => {
    console.log('🚀 Component mounted, fetching student dashboard...');
    fetchStudentDashboard();
  }, [fetchStudentDashboard]);

  // Fallback: Fetch streams even without faculty ID (for viewing only)
  useEffect(() => {
    console.log('🚀 Component mounted, fetching active streams as fallback...');
    fetchActiveStreams();
  }, [fetchActiveStreams]);

  useEffect(() => {
    console.log('🔄 useEffect triggered - facultyId:', facultyId);
    if (facultyId) {
      console.log('✅ Faculty ID available, fetching active streams...');
      fetchActiveStreams();
      // Set up polling for active streams
      const interval = setInterval(() => {
        console.log('🔄 Polling for active streams...');
        fetchActiveStreams();
      }, 30000); // Poll every 30 seconds
      return () => {
        console.log('🛑 Clearing active streams polling interval');
        clearInterval(interval);
      };
    } else {
      console.log('⚠️ No faculty ID available, fetching streams for viewing only');
      fetchActiveStreams();
    }
  }, [facultyId, fetchActiveStreams]);

  // Socket.IO setup for real-time notifications
  useEffect(() => {
    if (userId) {
      // Connect to correct Socket.IO endpoint for student
      const socketUrl = API_BASE_URL  // https://sasthra.in
      console.log('🌐 Student connecting to Socket.IO at:', `${socketUrl}/socket.io/`)

      const newSocket = io(socketUrl, {
        path: '/socket.io/',
        transports: ['websocket', 'polling'],
        forceNew: true,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 10000
      })

      newSocket.on('connect', () => {
        console.log('🔌 Student connected to Socket.IO');
        console.log('🔗 Socket ID:', newSocket.id);
        console.log('🌐 Socket URL:', API_BASE_URL);
        newSocket.emit('join_student_room', { student_id: userId });
        console.log('📡 Joined student room:', userId);
      });

      newSocket.on('joined_student_room', (data) => {
        console.log('✅ Successfully joined student room:', data);
      });

      newSocket.on('stream_permission_approved', (data) => {
        console.log('✅ Permission approved:', data);
        console.log('🔍 Approval data structure:', {
          session_id: data.session_id,
          message: data.message,
          approved_at: data.approved_at
        });

        toast.success('Permission approved! You can now join the stream.', {
          duration: 5000,
          icon: '🎉'
        });

        // Update permission status with session_id for joining
        setPermissionStatuses(prev => ({
          ...prev,
          [data.session_id]: {
            status: 'approved',
            can_join: true,
            response_message: data.message,
            approved_at: data.approved_at,
            session_id: data.session_id  // Store session_id for joining
          }
        }));

        // Find the stream and automatically join it
        const approvedStream = activeStreams.find(stream => stream.session_id === data.session_id);
        if (approvedStream) {
          console.log('🚀 Auto-joining approved stream:', approvedStream);
          // Auto-join the stream after a short delay
          setTimeout(() => {
            joinStream(approvedStream);
          }, 1000);
        } else {
          console.log('⚠️ Could not find stream with session_id:', data.session_id);
        }
      });

      newSocket.on('stream_permission_rejected', (data) => {
        console.log('❌ Permission rejected:', data);
        toast.error('Permission request rejected.', {
          duration: 5000,
          icon: '😞'
        });

        setPermissionStatuses(prev => ({
          ...prev,
          [data.session_id]: {
            status: 'rejected',
            can_join: false,
            response_message: data.message,
            rejected_at: data.rejected_at
          }
        }));
      });

      newSocket.on('disconnect', () => {
        console.log('🔌 Student disconnected from Socket.IO');
      });

      newSocket.on('connect_error', (error) => {
        console.error('❌ Student Socket.IO connection error:', error);
        console.error('🔍 Error details:', {
          message: error.message,
          description: error.description,
          context: error.context,
          type: error.type
        });
      });

      newSocket.on('reconnect', (attemptNumber) => {
        console.log('🔄 Student Socket.IO reconnected after', attemptNumber, 'attempts');
      });

      newSocket.on('reconnect_error', (error) => {
        console.error('❌ Student Socket.IO reconnection failed:', error);
      });

      setSocket(newSocket);

      return () => {
        newSocket.disconnect();
        setSocket(null);
      };
    } else {
      console.log('⚠️ No userId for Socket.IO connection');
    }
  }, [userId]);

  // Helper functions
  const formatUptime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getPermissionStatusIcon = (status) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'rejected':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'pending':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getPermissionStatusText = (status) => {
    switch (status) {
      case 'approved':
        return 'Approved';
      case 'rejected':
        return 'Rejected';
      case 'pending':
        return 'Pending';
      default:
        return 'Request Permission';
    }
  };

  const handleRequestPermission = (stream) => {
    if (!userId || !facultyId) {
      console.error('❌ Cannot open request modal:', { userId, facultyId });
      toast.error('Cannot request permission: Missing user or faculty information.');
      return;
    }
    setSelectedStream(stream);
    setRequestMessage('');
    setShowRequestModal(true);
  };

  const handleSubmitRequest = async () => {
    if (!selectedStream) return;

    const success = await requestStreamPermission(
      selectedStream.session_id,
      requestMessage || `Please allow me to join ${selectedStream.teacher_name}'s live class`
    );

    if (success) {
      setShowRequestModal(false);
      setSelectedStream(null);
      setRequestMessage('');
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: 'easeOut' }
    }
  };

  const streamCardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3 }
    },
    hover: {
      y: -5,
      scale: 1.02,
      transition: { duration: 0.2 }
    }
  };

  const buttonVariants = {
    idle: { scale: 1 },
    hover: { scale: 1.05 },
    tap: { scale: 0.95 }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 p-4 sm:p-8">
      <Toaster position="top-right" />

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-6xl mx-auto">

        {/* Header */}
        <motion.header
          variants={cardVariants}
          className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Video className="h-12 w-12 text-indigo-600" />
            <h1 className="text-4xl font-extrabold text-gray-900">Live Streaming</h1>
          </div>
          <p className="text-gray-600 text-xl">
            Join interactive live sessions from your teachers
          </p>
        </motion.header>

        {/* Loading State */}
        {isLoadingStreams && (
          <motion.div
            variants={cardVariants}
            className="bg-white/90 backdrop-blur-xl shadow-xl rounded-2xl p-8 text-center">
            <Loader2 className="h-12 w-12 text-indigo-500 animate-spin mx-auto mb-4" />
            <p className="text-lg text-gray-700">Loading active streams...</p>
          </motion.div>
        )}

        {/* No Streams Available */}
        {!isLoadingStreams && activeStreams.length === 0 && (
          <motion.div
            variants={cardVariants}
            className="bg-white/90 backdrop-blur-xl shadow-xl rounded-2xl p-8 text-center">
            <Video className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-700 mb-2">No Active Streams</h3>
            <p className="text-gray-600 mb-6">
              There are currently no live streams available. Check back later or contact your faculty.
            </p>
            <motion.button
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
              onClick={fetchActiveStreams}
              className="bg-indigo-600 text-white px-6 py-3 rounded-lg font-medium flex items-center gap-2 mx-auto">
              <RefreshCw className="h-5 w-5" />
              Refresh
            </motion.button>
          </motion.div>
        )}

        {/* Active Streams Grid */}
        {!isLoadingStreams && activeStreams.length > 0 && (
          <motion.div
            variants={cardVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {activeStreams.map((stream) => {
              const permissionStatus = permissionStatuses[stream.session_id];
              const isRequesting = isRequestingPermission[stream.session_id];

              return (
                <motion.div
                  key={stream.session_id}
                  variants={streamCardVariants}
                  whileHover="hover"
                  className="bg-white/90 backdrop-blur-xl shadow-xl rounded-2xl p-6 border border-gray-100">

                  {/* Stream Header */}
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-red-100 rounded-lg">
                      <Video className="h-6 w-6 text-red-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 truncate">
                        {stream.teacher_name}'s Live Class
                      </h3>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                        <span>Live</span>
                      </div>
                    </div>
                  </div>

                  {/* Stream Info */}
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <User className="h-4 w-4" />
                      <span>Teacher: {stream.teacher_name}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Users className="h-4 w-4" />
                      <span>{stream.viewer_count} viewers</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span>Live for {formatUptime(stream.uptime)}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Play className="h-4 w-4" />
                      <span>Quality: {stream.quality}</span>
                    </div>
                  </div>

                  {/* Permission Status */}
                  {permissionStatus && (
                    <div className="mb-4 p-3 rounded-lg bg-gray-50">
                      <div className="flex items-center gap-2">
                        {getPermissionStatusIcon(permissionStatus.status)}
                        <span className="text-sm font-medium">
                          {getPermissionStatusText(permissionStatus.status)}
                        </span>
                      </div>
                      {permissionStatus.response_message && (
                        <p className="text-sm text-gray-600 mt-1">
                          {permissionStatus.response_message}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="space-y-2">
                    {permissionStatus?.status === 'approved' ? (
                      <motion.button
                        variants={buttonVariants}
                        whileHover="hover"
                        whileTap="tap"
                        onClick={() => joinStream(stream)}
                        className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center gap-2">
                        <Play className="h-5 w-5" />
                        Join Stream
                      </motion.button>
                    ) : permissionStatus?.status === 'pending' ? (
                      <div className="w-full bg-yellow-100 text-yellow-800 py-3 px-4 rounded-lg font-medium text-center">
                        Permission Pending...
                      </div>
                    ) : permissionStatus?.status === 'rejected' ? (
                      <div className="w-full bg-red-100 text-red-800 py-3 px-4 rounded-lg font-medium text-center">
                        Permission Rejected
                      </div>
                    ) : (
                      <motion.button
                        variants={buttonVariants}
                        whileHover="hover"
                        whileTap="tap"
                        onClick={() => handleRequestPermission(stream)}
                        disabled={isRequesting}
                        className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center gap-2 disabled:opacity-50">
                        {isRequesting ? (
                          <>
                            <Loader2 className="h-5 w-5 animate-spin" />
                            Requesting...
                          </>
                        ) : (
                          <>
                            <Send className="h-5 w-5" />
                            Request Permission
                          </>
                        )}
                      </motion.button>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </motion.div>
        )}

        {/* Request Permission Modal */}
        <AnimatePresence>
          {showRequestModal && selectedStream && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
              onClick={() => setShowRequestModal(false)}>

              <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
                className="bg-white rounded-2xl p-6 w-full max-w-md">

                <div className="flex items-center gap-3 mb-4">
                  <MessageSquare className="h-6 w-6 text-indigo-600" />
                  <h3 className="text-xl font-semibold">Request Permission</h3>
                </div>

                <p className="text-gray-600 mb-4">
                  Send a request to join <strong>{selectedStream.teacher_name}'s</strong> live class
                </p>

                <textarea
                  value={requestMessage}
                  onChange={(e) => setRequestMessage(e.target.value)}
                  placeholder="Please allow me to join the live class..."
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none h-24 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                />

                <div className="flex gap-3 mt-6">
                  <motion.button
                    variants={buttonVariants}
                    whileHover="hover"
                    whileTap="tap"
                    onClick={() => setShowRequestModal(false)}
                    className="flex-1 bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium">
                    Cancel
                  </motion.button>
                  <motion.button
                    variants={buttonVariants}
                    whileHover="hover"
                    whileTap="tap"
                    onClick={handleSubmitRequest}
                    className="flex-1 bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center gap-2">
                    <Send className="h-4 w-4" />
                    Send Request
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default LiveStreaming;